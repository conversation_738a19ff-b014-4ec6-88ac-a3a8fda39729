{% extends 'base.html' %}

{% block title %}Deactivate Account - CozyWish{% endblock %}

{% block extra_css %}
{% load static %}
{% load widget_tweaks %}

<!-- Google Fonts -->
<link rel="preconnect" href="https://fonts.googleapis.com">
<link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
<link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=Poppins:wght@400;500;600;700;800&family=Playfair+Display:wght@400;500;600;700&display=swap" rel="stylesheet">

<style>
    /* CozyWish Design System - Customer Deactivate Account */
    :root {
        /* Brand Colors */
        --cw-brand-primary: #2F160F;
        --cw-brand-light: #4a2a1f;
        --cw-brand-accent: #fae1d7;
        --cw-accent-light: #fef7f0;
        --cw-accent-dark: #f1d4c4;

        /* Neutral Colors */
        --cw-neutral-600: #525252;
        --cw-neutral-700: #404040;
        --cw-neutral-800: #262626;

        /* Typography */
        --cw-font-primary: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
        --cw-font-heading: 'Poppins', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
        --cw-font-display: 'Playfair Display', Georgia, 'Times New Roman', serif;

        /* Shadows */
        --cw-shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
        --cw-shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
        --cw-shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);

        /* Gradients */
        --cw-gradient-hero: radial-gradient(ellipse at center, var(--cw-brand-accent) 40%, var(--cw-accent-light) 70%, #ffffff 100%);
        --cw-gradient-brand-button: linear-gradient(135deg, var(--cw-brand-primary) 0%, var(--cw-brand-light) 100%);
        --cw-gradient-card: linear-gradient(135deg, #ffffff 0%, var(--cw-brand-accent) 100%);
        --cw-gradient-card-subtle: linear-gradient(135deg, #ffffff 0%, var(--cw-accent-light) 100%);
    }

    /* Global Styles */
    body {
        font-family: var(--cw-font-primary);
        line-height: 1.6;
        color: var(--cw-neutral-800);
        background: white;
        min-height: 100vh;
    }

    h1, h2, h3, h4, h5, h6 {
        font-family: var(--cw-font-heading);
        font-weight: 600;
        color: var(--cw-brand-primary);
        line-height: 1.3;
    }

    /* Deactivate Account Section */
    .deactivate-section {
        padding: 5rem 0;
        background: white;
        min-height: 100vh;
        display: flex;
        align-items: center;
    }

    .deactivate-container {
        max-width: 800px;
        width: 100%;
        margin: 0 auto;
        padding: 0 2rem;
    }

    .deactivate-card {
        background: white;
        border: 1px solid rgba(250, 225, 215, 0.3);
        border-radius: 1rem;
        box-shadow: var(--cw-shadow-lg);
        overflow: hidden;
        width: 100%;
    }

    /* Header Section */
    .deactivate-header {
        background: linear-gradient(135deg, #fef2f2 0%, #fee2e2 100%);
        padding: 3rem 3rem 2rem;
        text-align: center;
        position: relative;
        border-bottom: 1px solid #fecaca;
    }

    .deactivate-header::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="warning-pattern" x="0" y="0" width="30" height="30" patternUnits="userSpaceOnUse"><path d="M15,5 L20,15 L10,15 Z" fill="%23fecaca" opacity="0.4"/></pattern></defs><rect width="100" height="100" fill="url(%23warning-pattern)"/></svg>') repeat;
        opacity: 0.6;
        z-index: 1;
    }

    .deactivate-header .content {
        position: relative;
        z-index: 2;
    }

    .deactivate-icon {
        width: 80px;
        height: 80px;
        background: linear-gradient(135deg, #dc2626 0%, #b91c1c 100%);
        border-radius: 50%;
        display: inline-flex;
        align-items: center;
        justify-content: center;
        color: white;
        font-size: 2rem;
        margin-bottom: 1.5rem;
        box-shadow: var(--cw-shadow-md);
    }

    .deactivate-title {
        font-family: var(--cw-font-display);
        font-size: 2.5rem;
        font-weight: 700;
        color: #dc2626;
        margin-bottom: 1rem;
        line-height: 1.2;
    }

    .deactivate-subtitle {
        font-size: 1.125rem;
        color: var(--cw-neutral-600);
        margin-bottom: 0;
        line-height: 1.6;
        max-width: 500px;
        margin-left: auto;
        margin-right: auto;
    }

    .deactivate-body {
        padding: 3rem;
    }

    /* Warning Info Card */
    .warning-info {
        background: linear-gradient(135deg, #fef2f2 0%, #fee2e2 100%);
        border: 2px solid #fecaca;
        border-radius: 1rem;
        padding: 2rem;
        margin-bottom: 2rem;
        position: relative;
        overflow: hidden;
    }

    .warning-info::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="info-pattern" x="0" y="0" width="20" height="20" patternUnits="userSpaceOnUse"><circle cx="10" cy="10" r="1" fill="%23fecaca" opacity="0.5"/></pattern></defs><rect width="100" height="100" fill="url(%23info-pattern)"/></svg>') repeat;
        opacity: 0.6;
        z-index: 1;
    }

    .warning-info .content {
        position: relative;
        z-index: 2;
    }

    .warning-info h5 {
        color: #dc2626;
        font-weight: 700;
        margin-bottom: 1rem;
        font-size: 1.375rem;
        font-family: var(--cw-font-heading);
        display: flex;
        align-items: center;
        gap: 0.75rem;
    }

    .warning-info p {
        color: var(--cw-neutral-700);
        margin-bottom: 1rem;
        font-weight: 600;
        font-family: var(--cw-font-primary);
    }

    .warning-info ul {
        color: var(--cw-neutral-700);
        margin-bottom: 0;
        padding-left: 1.5rem;
    }

    .warning-info li {
        margin-bottom: 0.75rem;
        font-weight: 500;
        line-height: 1.5;
    }

    .warning-info li:last-child {
        margin-bottom: 0;
    }

    /* Form Styling */
    .form-control-cw {
        border: 2px solid var(--cw-brand-accent);
        border-radius: 0.5rem;
        padding: 0.875rem 1rem;
        font-size: 1rem;
        transition: all 0.2s ease;
        background: white;
        color: var(--cw-neutral-800);
        font-family: var(--cw-font-primary);
    }

    .form-control-cw:focus {
        border-color: #dc2626;
        box-shadow: 0 0 0 0.2rem rgba(220, 38, 38, 0.1);
        outline: none;
    }

    .form-control-cw.is-invalid {
        border-color: #dc2626;
        box-shadow: 0 0 0 0.2rem rgba(220, 38, 38, 0.1);
    }

    .form-label {
        color: var(--cw-neutral-700);
        font-weight: 600;
        font-family: var(--cw-font-heading);
        margin-bottom: 0.5rem;
    }

    /* Button Styling */
    .btn-cw-danger {
        background: linear-gradient(135deg, #dc2626 0%, #b91c1c 100%);
        border: none;
        border-radius: 0.5rem;
        font-weight: 600;
        padding: 1rem 2rem;
        color: white;
        transition: all 0.2s ease;
        box-shadow: var(--cw-shadow-sm);
        font-family: var(--cw-font-heading);
        letter-spacing: 0.025em;
        text-decoration: none;
        display: inline-flex;
        align-items: center;
        gap: 0.5rem;
    }

    .btn-cw-danger:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(220, 38, 38, 0.3);
        color: white;
        text-decoration: none;
    }

    .btn-cw-secondary {
        border: 2px solid var(--cw-brand-primary);
        color: var(--cw-brand-primary);
        border-radius: 0.5rem;
        font-weight: 600;
        padding: 1rem 2rem;
        background: white;
        transition: all 0.2s ease;
        text-decoration: none;
        display: inline-flex;
        align-items: center;
        font-family: var(--cw-font-heading);
        letter-spacing: 0.025em;
        gap: 0.5rem;
    }

    .btn-cw-secondary:hover {
        background: var(--cw-brand-primary);
        color: white;
        transform: translateY(-2px);
        text-decoration: none;
        box-shadow: var(--cw-shadow-md);
    }

    /* Alert Styling */
    .alert-cw {
        border-radius: 0.5rem;
        padding: 1rem 1.25rem;
        margin-bottom: 1rem;
        border: 1px solid;
        font-family: var(--cw-font-primary);
    }

    .alert-cw-error {
        background: #fef2f2;
        border-color: #fecaca;
        color: #991b1b;
    }

    /* Error Messages */
    .invalid-feedback {
        display: block !important;
        width: 100%;
        margin-top: 0.5rem;
        font-size: 0.875rem;
        color: #dc2626;
        font-weight: 500;
        font-family: var(--cw-font-primary);
    }

    .invalid-feedback i {
        margin-right: 0.25rem;
    }

    /* Form Text */
    .form-text {
        margin-top: 0.5rem;
        font-size: 0.875rem;
        color: var(--cw-neutral-600);
        font-family: var(--cw-font-primary);
    }

    /* Responsive Design */
    @media (max-width: 768px) {
        .deactivate-section {
            padding: 3rem 0;
        }

        .deactivate-container {
            max-width: 700px;
            padding: 0 1.5rem;
        }

        .deactivate-header {
            padding: 2rem 2rem 1.5rem;
        }

        .deactivate-title {
            font-size: 2rem;
        }

        .deactivate-subtitle {
            font-size: 1rem;
        }

        .deactivate-body {
            padding: 2rem;
        }

        .btn-cw-danger,
        .btn-cw-secondary {
            width: 100%;
            justify-content: center;
        }

        .warning-info {
            padding: 1.5rem;
        }
    }

    @media (max-width: 576px) {
        .deactivate-container {
            padding: 0 1rem;
        }

        .deactivate-header {
            padding: 1.5rem 1.5rem 1rem;
        }

        .deactivate-body {
            padding: 1.5rem;
        }

        .deactivate-title {
            font-size: 1.75rem;
        }

        .deactivate-icon {
            width: 64px;
            height: 64px;
            font-size: 1.5rem;
        }

        .warning-info {
            padding: 1.25rem;
        }
    }
</style>
{% endblock %}

{% block content %}
<section class="deactivate-section">
    <div class="deactivate-container">
        <div class="deactivate-card">
            <div class="deactivate-header">
                <div class="content">
                    <div class="deactivate-icon">
                        <i class="fas fa-exclamation-triangle"></i>
                    </div>
                    <h1 class="deactivate-title">Deactivate Account</h1>
                    <p class="deactivate-subtitle">This action will temporarily disable your account</p>
                </div>
            </div>

            <div class="deactivate-body">
                <div class="warning-info">
                    <div class="content">
                        <h5><i class="fas fa-info-circle"></i>Important Information</h5>
                        <p>This is a permanent action that cannot be undone. Deactivating your account will:</p>
                        <ul>
                            <li>You will lose access to your account</li>
                            <li>Your bookings history will be preserved</li>
                            <li>Hide your profile from other users</li>
                            <li>Preserve your data securely</li>
                            <li>Allow reactivation by contacting support</li>
                        </ul>
                    </div>
                </div>

                <form method="post" novalidate>
                  {% csrf_token %}

                  {% if form.non_field_errors %}
                  <div class="alert-cw alert-cw-error mb-4">
                    {% for error in form.non_field_errors %}
                    <i class="fas fa-exclamation-circle me-2"></i>{{ error }}
                    {% endfor %}
                  </div>
                  {% endif %}

                  <div class="mb-4">
                    <label for="{{ form.confirm_email.id_for_label }}" class="form-label">
                      Type your email to confirm deactivation
                    </label>
                    {% if form.confirm_email.errors %}
                      {{ form.confirm_email|add_class:"form-control-cw is-invalid"|attr:"placeholder:Enter your email to confirm" }}
                    {% else %}
                      {{ form.confirm_email|add_class:"form-control-cw"|attr:"placeholder:Enter your email to confirm" }}
                    {% endif %}
                    {% if form.confirm_email.errors %}
                    <div class="invalid-feedback" role="alert" aria-live="polite">
                      {% for error in form.confirm_email.errors %}
                      <i class="fas fa-exclamation-circle"></i>{{ error }}
                      {% endfor %}
                    </div>
                    {% endif %}
                    {% if form.confirm_email.help_text %}
                    <div class="form-text">{{ form.confirm_email.help_text }}</div>
                    {% endif %}
                  </div>

                  <div class="d-flex flex-column flex-sm-row gap-3 justify-content-between">
                    <a href="{% url 'accounts_app:customer_profile' %}" class="btn-cw-secondary">
                      <i class="fas fa-arrow-left"></i>Cancel
                    </a>
                    <button type="submit" class="btn-cw-danger">
                      <i class="fas fa-user-slash"></i>Deactivate Account
                    </button>
                  </div>
                </form>
            </div>
        </div>
    </div>
</section>
